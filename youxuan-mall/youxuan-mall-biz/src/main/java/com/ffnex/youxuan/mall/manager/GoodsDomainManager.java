package com.ffnex.youxuan.mall.manager;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ffnex.youxuan.mall.entity.CustomerPricingRuleEntity;
import com.ffnex.youxuan.mall.entity.GoodsEntity;
import com.ffnex.youxuan.mall.entity.GoodsRecommendationEntity;
import com.ffnex.youxuan.mall.entity.GoodsServiceTagEntity;
import com.ffnex.youxuan.mall.entity.GoodsSkuEntity;
import com.ffnex.youxuan.mall.entity.TieredPricingRuleEntity;
import com.ffnex.youxuan.mall.mapper.CustomerPricingRuleMapper;
import com.ffnex.youxuan.mall.mapper.GoodsRecommendationMapper;
import com.ffnex.youxuan.mall.mapper.GoodsServiceTagMapper;
import com.ffnex.youxuan.mall.mapper.GoodsSkuMapper;
import com.ffnex.youxuan.mall.mapper.TieredPricingRuleMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

// 处理商品相关逻辑 提取自service中
@Component
@RequiredArgsConstructor
public class GoodsDomainManager {

    private final GoodsSkuMapper goodsSkuMapper;
    private final GoodsServiceTagMapper goodsServiceTagMapper;
    private final GoodsRecommendationMapper goodsRecommendationMapper;
    private final TieredPricingRuleMapper tieredPricingRuleMapper;
    private final CustomerPricingRuleMapper customerPricingRuleMapper;

    public void handleGoodsRelations(GoodsEntity goods) {
        // 处理商品SKU关系：先删除所有现有的SKU，然后重新插入
        goodsSkuMapper.delete(Wrappers.lambdaQuery(GoodsSkuEntity.class).eq(GoodsSkuEntity::getGoodsId, goods.getId()));
        if (goods.getGoodsSkuList() != null) {
            for (GoodsSkuEntity goodsSku : goods.getGoodsSkuList()) {
                // 重置ID，确保作为新记录插入
                goodsSku.setId(null);
                goodsSku.setGoodsId(goods.getId());
                goodsSkuMapper.insert(goodsSku);

                // 插入SKU后立即处理该SKU的定价规则，确保SKU ID已经生成
                handleSkuPricingRules(goodsSku);
            }
        }
        

        goodsServiceTagMapper.delete(Wrappers.lambdaQuery(GoodsServiceTagEntity.class).eq(GoodsServiceTagEntity::getGoodsId, goods.getId()));
        if (goods.getServiceTagIds() != null) {
            for (Long serviceTagId : goods.getServiceTagIds()) {
                GoodsServiceTagEntity goodsServiceTag = new GoodsServiceTagEntity();
                goodsServiceTag.setServiceTagId(serviceTagId);
                goodsServiceTag.setGoodsId(goods.getId());
                goodsServiceTagMapper.insert(goodsServiceTag);
            }
        }

        goodsRecommendationMapper.delete(Wrappers.lambdaQuery(GoodsRecommendationEntity.class).eq(GoodsRecommendationEntity::getGoodsId, goods.getId()));
        if (goods.getPositionIds() != null) {
            for (Long positionId : goods.getPositionIds()) {
                GoodsRecommendationEntity goodsRecommendationEntity = new GoodsRecommendationEntity();
                goodsRecommendationEntity.setGoodsId(goods.getId());
                goodsRecommendationEntity.setPositionId(positionId);
                goodsRecommendationMapper.insert(goodsRecommendationEntity);
            }
        }
    }

    /**
     * 处理SKU的定价规则
     * @param goodsSku SKU实体，必须已经插入数据库并拥有有效的ID
     */
    private void handleSkuPricingRules(GoodsSkuEntity goodsSku) {
        // 确保SKU ID不为空
        if (goodsSku.getId() == null) {
            throw new IllegalStateException("SKU ID不能为空，请确保SKU已经被正确插入数据库");
        }

        // 处理阶梯定价规则
        if (goodsSku.getTieredPricingRuleList() != null) {
            // 先删除该SKU的所有阶梯定价规则
            tieredPricingRuleMapper.delete(Wrappers.lambdaQuery(TieredPricingRuleEntity.class)
                    .eq(TieredPricingRuleEntity::getSkuId, goodsSku.getId()));

            // 重新插入阶梯定价规则
            for (TieredPricingRuleEntity tieredPricingRule : goodsSku.getTieredPricingRuleList()) {
                tieredPricingRule.setId(null); // 重置ID，确保作为新记录插入
                tieredPricingRule.setSkuId(goodsSku.getId());
                tieredPricingRuleMapper.insert(tieredPricingRule);
            }
        }

        // 处理客户定价规则
        if (goodsSku.getCustomerPricingRuleList() != null) {
            // 先删除该SKU的所有客户定价规则
            customerPricingRuleMapper.delete(Wrappers.lambdaQuery(CustomerPricingRuleEntity.class)
                    .eq(CustomerPricingRuleEntity::getSkuId, goodsSku.getId()));

            // 重新插入客户定价规则
            for (CustomerPricingRuleEntity customerPricingRule : goodsSku.getCustomerPricingRuleList()) {
                customerPricingRule.setId(null); // 重置ID，确保作为新记录插入
                customerPricingRule.setSkuId(goodsSku.getId());
                customerPricingRuleMapper.insert(customerPricingRule);
            }
        }
    }
}
